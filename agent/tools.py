"""Tool implementations for the LangGraph agent."""

import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from typing import Optional
from langchain_core.tools import tool

from config.settings import settings
from agent.state import EmailMessage

logger = logging.getLogger(__name__)


@tool
def create_email(to_email: str, subject: str, body: str, from_name: Optional[str] = None) -> str:
    """
    Create an email message that will be queued for sending.

    This tool creates an EmailMessage object instead of directly sending the email.
    The email will be added to the pending emails list and shown to the user for approval.

    Args:
        to_email: Recipient email address
        subject: Email subject
        body: Email body content
        from_name: Optional sender name (defaults to configured email)

    Returns:
        Confirmation message in German with email details
    """
    try:
        # Create email message object
        email_message = EmailMessage(
            to_email=to_email,
            subject=subject,
            body=body,
            from_name=from_name
        )

        # Return structured data that can be parsed by the node
        # We use a special format that the node can recognize and extract
        return f"EMAIL_CREATED:{email_message.model_dump_json()}"

    except Exception as e:
        error_msg = f"<PERSON><PERSON> beim <PERSON> der <PERSON>ail: {str(e)}"
        logger.error(error_msg)
        return error_msg


# List of available tools for the agent
TOOLS = [create_email]
